"""Service for converting various file types to Markdown."""

import logging
import os
import json
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from markitdown import MarkItDown
from typing import Optional
import io

# Import the image processing service
from services.image_processing_service import process_pdf_with_ocr, process_image_with_ocr

# Configure logger
logger = logging.getLogger(__name__)

def is_file_valid(file_path: str) -> bool:
    """
    Check if file exists and is readable.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if file is valid, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return False

        if not os.path.isfile(file_path):
            logger.error(f"Path is not a file: {file_path}")
            return False

        if os.path.getsize(file_path) == 0:
            logger.warning(f"File is empty: {file_path}")
            return False

        # Try to read first few bytes to check if file is readable
        with open(file_path, 'rb') as f:
            f.read(10)

        return True

    except Exception as e:
        logger.error(f"File validation failed for {file_path}: {str(e)}")
        return False

def convert_file_to_markdown(file_path: str, use_ocr: bool = True) -> str:
    """
    Convert a file to Markdown format using markitdown library or OCR for PDFs and images.

    Args:
        file_path (str): Path to the file to convert
        use_ocr (bool): Whether to use OCR for PDFs and images

    Returns:
        str: The Markdown content

    Raises:
        Exception: If conversion fails
    """
    try:
        # Validate file first
        if not is_file_valid(file_path):
            raise Exception(f"File validation failed: {file_path}")

        # Get file extension for file type detection
        file_extension = Path(file_path).suffix.lower()
        logger.info(f"Converting file to Markdown: {file_path} (type: {file_extension})")

        # Create MarkItDown converter instance
        converter = MarkItDown()
        markdown_content = ""

        # Convert based on file type
        if file_extension in ['.pdf']:
            markdown_content = convert_pdf_to_markdown(file_path, converter, use_ocr)

        # Handle image files with OCR
        elif file_extension in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.gif']:
            if use_ocr:
                # Use OCR for image processing
                logger.info("Processing image with OCR")
                ocr_text = process_image_with_ocr(
                    image_path=file_path,
                    lang="eng+vie",
                    apply_grayscale=True,
                    apply_sharpen=True,
                    apply_threshold=True,
                    threshold_value=150
                )
                # Format the OCR text as markdown
                markdown_content = format_ocr_text_as_markdown(ocr_text)
            else:
                # For images without OCR, create a simple markdown with image reference
                logger.info("Creating markdown with image reference")
                image_filename = os.path.basename(file_path)
                markdown_content = f"![{image_filename}]({file_path})\n\n"

        elif file_extension in ['.docx', '.doc']:
            # Word document conversion
            logger.info("Converting Word document to Markdown")
            result = converter.convert(file_path)
            markdown_content = result.text if hasattr(result, 'text') else str(result)

        elif file_extension in ['.pptx', '.ppt']:
            # PowerPoint presentation conversion
            logger.info("Converting PowerPoint to Markdown")
            result = converter.convert(file_path)
            markdown_content = result.text if hasattr(result, 'text') else str(result)

        elif file_extension in ['.xlsx', '.xls']:
            # Excel spreadsheet conversion
            logger.info("Converting Excel to Markdown")
            result = converter.convert(file_path)
            markdown_content = result.text if hasattr(result, 'text') else str(result)

        elif file_extension in ['.txt', '.md']:
            # Text file - read directly
            logger.info("Reading text/markdown file")
            with open(file_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

        elif file_extension in ['.html', '.htm']:
            # HTML conversion
            logger.info("Converting HTML to Markdown")
            result = converter.convert(file_path)
            markdown_content = result.text if hasattr(result, 'text') else str(result)

        else:
            # Unsupported file type - attempt generic conversion
            logger.warning(f"Unsupported file type: {file_extension}, attempting generic conversion")
            result = converter.convert(file_path)
            markdown_content = result.text if hasattr(result, 'text') else str(result)

        # Debug: Ghi log để kiểm tra loại và nội dung
        logger.debug(f"Type of markdown_content: {type(markdown_content)}")
        logger.debug(f"Markdown content preview: {markdown_content[:200]}")

        # Clean up the markdown content
        markdown_content = clean_markdown(markdown_content)

        logger.info(f"Successfully converted file to Markdown ({len(markdown_content)} characters)")
        return markdown_content

    except Exception as e:
        logger.error(f"Error converting file to Markdown: {str(e)}")
        raise Exception(f"Failed to convert file to Markdown: {str(e)}")

def format_ocr_text_as_markdown(ocr_text: str) -> str:
    """
    Format OCR text as markdown with proper structure.

    Args:
        ocr_text (str): Raw OCR text

    Returns:
        str: Formatted markdown text
    """
    if not ocr_text:
        return ""

    # Split text into paragraphs
    paragraphs = ocr_text.split("\n\n")

    # Format each paragraph
    formatted_paragraphs = []
    for paragraph in paragraphs:
        # Skip empty paragraphs
        if not paragraph.strip():
            continue

        # Check if paragraph looks like a heading (short, ends with colon, all caps, etc.)
        lines = paragraph.split("\n")
        formatted_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Heuristic for headings: short lines that are all caps or end with colon
            if (len(line) < 100 and (line.isupper() or line.endswith(":"))) or i == 0:
                # Make it a heading based on length and position
                if len(line) < 20:
                    formatted_lines.append(f"## {line}")
                elif len(line) < 50:
                    formatted_lines.append(f"### {line}")
                else:
                    formatted_lines.append(f"#### {line}")
            else:
                # Regular paragraph text
                formatted_lines.append(line)

        # Join the formatted lines
        formatted_paragraph = "\n".join(formatted_lines)
        formatted_paragraphs.append(formatted_paragraph)

    # Join all formatted paragraphs with double newlines
    return "\n\n".join(formatted_paragraphs)

def clean_markdown(markdown_content: str) -> str:
    """
    Clean up markdown content for better processing.

    Args:
        markdown_content (str): Raw markdown content

    Returns:
        str: Cleaned markdown content
    """
    # Kiểm tra nếu markdown_content không phải chuỗi
    if not isinstance(markdown_content, str):
        logger.warning(f"markdown_content is not a string, type: {type(markdown_content)}. Converting to string.")
        markdown_content = str(markdown_content)

    if not markdown_content:
        return ""

    # Normalize line endings
    markdown_content = markdown_content.replace('\r\n', '\n').replace('\r', '\n')

    # Remove consecutive blank lines (limit to max 2)
    lines = markdown_content.split('\n')
    cleaned_lines = []
    blank_count = 0

    for line in lines:
        if line.strip() == '':
            blank_count += 1
            if blank_count <= 2:  # Keep max 2 consecutive blank lines
                cleaned_lines.append(line)
        else:
            blank_count = 0
            cleaned_lines.append(line)

    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)

    # Remove trailing whitespace on each line
    cleaned_content = '\n'.join(line.rstrip() for line in cleaned_content.split('\n'))

    return cleaned_content
